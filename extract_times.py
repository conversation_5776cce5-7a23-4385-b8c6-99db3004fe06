#!/usr/bin/env python3
"""
Script to extract time values from market orderbook JSON files and write them to CSV.

This script reads JSON files matching the pattern 'market-orderbooks.*.json',
extracts the 'time' values from each file, and writes them line by line to a CSV file
without headers.
"""

import json
import csv
import glob
import os
import sys
from pathlib import Path


def extract_times_from_json(file_path):
    """
    Extract all time values from a JSON file.

    Args:
        file_path (str): Path to the JSON file

    Returns:
        list: List of time values found in the file, empty list if none found
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        time_values = []

        # Extract all time values from data array
        if 'data' in data and isinstance(data['data'], list):
            for entry in data['data']:
                if isinstance(entry, dict) and 'time' in entry:
                    time_values.append(entry['time'])

        if not time_values:
            print(f"Warning: No time fields found in {file_path}")

        return time_values

    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in {file_path}: {e}")
        return []
    except FileNotFoundError:
        print(f"Error: File not found: {file_path}")
        return []
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []


def create_csv_filename(json_filename):
    """
    Create CSV filename from JSON filename.

    Args:
        json_filename (str): Original JSON filename

    Returns:
        str: CSV filename with .csv extension
    """
    # Remove path and .json extension, add .csv extension
    base_name = os.path.splitext(os.path.basename(json_filename))[0]
    return f"{base_name}.csv"


def main():
    """Main function to process JSON files and create CSV output."""

    # Define the pattern for JSON files
    json_pattern = "files/market-orderbooks.*.json"

    # Find all matching JSON files
    json_files = glob.glob(json_pattern)

    if not json_files:
        print(f"No files found matching pattern: {json_pattern}")
        print("Make sure the files are in the 'files' directory.")
        return

    print(f"Found {len(json_files)} JSON files:")
    for file in sorted(json_files):
        print(f"  - {file}")

    # Process each JSON file separately
    for json_file in sorted(json_files):
        print(f"\nProcessing: {json_file}")

        # Extract all time values from this file
        time_values = extract_times_from_json(json_file)

        if time_values:
            print(f"  Found {len(time_values)} time values")

            # Create output CSV filename
            output_csv = create_csv_filename(json_file)

            # Write to CSV file
            try:
                with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # Write each time value as a separate row (no header)
                    for time_value in time_values:
                        writer.writerow([time_value])

                print(f"  Successfully wrote {len(time_values)} time values to {output_csv}")

            except Exception as e:
                print(f"  Error writing to CSV file {output_csv}: {e}")
        else:
            print(f"  No time values found in {json_file}")

    print("\nProcessing complete!")


if __name__ == "__main__":
    main()
