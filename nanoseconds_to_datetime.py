from datetime import datetime, timezone, date
import math


def nanoseconds_to_datetime(nanoseconds, use_utc=True):
    """
    Convert nanoseconds since Unix epoch to datetime object.

    Args:
        nanoseconds (int): Nanoseconds since Unix epoch (January 1, 1970)
        use_utc (bool): If True, return UTC datetime; if False, return local datetime

    Returns:
        datetime: Datetime object (precision limited to microseconds)
    """
    # Convert nanoseconds to seconds
    seconds = nanoseconds / 1_000_000_000

    if use_utc:
        return datetime.fromtimestamp(seconds, tz=timezone.utc)
    else:
        return datetime.fromtimestamp(seconds)


def nanoseconds_to_datetime_precise(nanoseconds, use_utc=True):
    """
    Convert nanoseconds to datetime with nanosecond precision tracking.

    Args:
        nanoseconds (int): Nanoseconds since Unix epoch
        use_utc (bool): If True, return UTC datetime; if False, return local datetime

    Returns:
        tuple: (datetime object, remaining_nanoseconds)
    """
    # Split into seconds and remaining nanoseconds
    seconds = nanoseconds // 1_000_000_000
    remaining_ns = nanoseconds % 1_000_000_000

    # Convert to microseconds for datetime (loses some precision)
    microseconds = remaining_ns // 1000
    remaining_ns_after_us = remaining_ns % 1000

    # Create datetime with microsecond precision
    if use_utc:
        dt = datetime.fromtimestamp(seconds + microseconds / 1_000_000, tz=timezone.utc)
    else:
        dt = datetime.fromtimestamp(seconds + microseconds / 1_000_000)

    return dt, remaining_ns_after_us


def datetime_to_nanoseconds(dt):
    """
    Convert datetime object to nanoseconds since Unix epoch.

    Args:
        dt (datetime): Datetime object

    Returns:
        int: Nanoseconds since Unix epoch
    """
    return int(dt.timestamp() * 1_000_000_000)


def format_nanoseconds_readable(nanoseconds):
    """
    Format nanoseconds in a human-readable way.

    Args:
        nanoseconds (int): Nanoseconds value

    Returns:
        str: Formatted string
    """
    seconds = nanoseconds // 1_000_000_000
    remaining_ns = nanoseconds % 1_000_000_000
    ms = remaining_ns // 1_000_000
    us = (remaining_ns % 1_000_000) // 1000
    ns = remaining_ns % 1000

    return f"{seconds}s {ms}ms {us}μs {ns}ns"


# Example usage
if __name__ == "__main__":
    # Example nanosecond timestamps
    test_nanoseconds = [
        1703131502123456789,  # 2023-12-21 04:05:02.123457 UTC
        1703138942123456789,  # 2023-12-21 07:10:02.123457 UTC
    ]

    print("Converting nanoseconds to datetime:")
    print("=" * 60)

    for ns in test_nanoseconds:
        print(f"Nanoseconds: {ns}")
        print(f"Readable:    {format_nanoseconds_readable(ns)}")

        # Standard conversion (loses nanosecond precision)
        dt_utc = nanoseconds_to_datetime(ns, use_utc=True)
        dt_local = nanoseconds_to_datetime(ns, use_utc=False)

        print(f"UTC:         {dt_utc}")
        print(f"Local:       {dt_local}")

        # Precise conversion (tracks lost nanoseconds)
        dt_precise, remaining_ns = nanoseconds_to_datetime_precise(ns, use_utc=True)
        print(f"Precise UTC: {dt_precise} + {remaining_ns}ns")
        print("-" * 60)

    # Demonstrate precision limitations
    print("\nPrecision demonstration:")
    print("=" * 40)

    # Create a timestamp with nanosecond precision
    test_ns = 1721088000123456789  # Has nanosecond precision

    print(f"Original nanoseconds: {test_ns}")
    print(f"Readable format:      {format_nanoseconds_readable(test_ns)}")

    # Convert to datetime and back
    dt = nanoseconds_to_datetime(test_ns)
    back_to_ns = date
