import requests
import time
from urllib.parse import quote

# Base URL template
base_url = "https://api.coinmetrics.io/v4/timeseries/market-orderbooks"

# Parameters
params = {
    'depth_limit': '1',
    'start_time': '2025-07-11T23:30:00',
    'end_time': '2025-07-11T23:59:59',
    'granularity': '1d',
    'paging_from': 'start',
    'page_size': '10000',
    'limit_per_market': '1',
    'timezone': 'UTC',
    'pretty': 'true',
    'api_key': '4ze0LiIhSN10fy8iBnUH'  # Add your API key here
}

# List of markets
markets = [
    "deribit-BTC-12JUL25-100000-C-option",
    "deribit-BTC-12JUL25-100000-P-option",
    "deribit-BTC-12JUL25-102000-C-option",
    "deribit-BTC-12JUL25-102000-P-option",
    "deribit-BTC-12JUL25-104000-C-option",
    "deribit-BTC-12JUL25-104000-P-option",
    "deribit-BTC-12JUL25-105000-C-option",
    "deribit-BTC-12JUL25-105000-P-option",
    "deribit-BTC-12JUL25-106000-C-option",
    "deribit-BTC-12JUL25-106000-P-option",
    "deribit-BTC-12JUL25-107000-C-option",
    "deribit-BTC-12JUL25-107000-P-option",
    "deribit-BTC-12JUL25-108000-C-option",
    "deribit-BTC-12JUL25-108000-P-option",
    "deribit-BTC-12JUL25-109000-C-option",
    "deribit-BTC-12JUL25-109000-P-option",
    "deribit-BTC-12JUL25-110000-C-option",
    "deribit-BTC-12JUL25-110000-P-option",
    "deribit-BTC-12JUL25-111000-C-option",
    "deribit-BTC-12JUL25-111000-P-option",
    "deribit-BTC-12JUL25-112000-C-option",
    "deribit-BTC-12JUL25-112000-P-option",
    "deribit-BTC-12JUL25-113000-C-option",
    "deribit-BTC-12JUL25-113000-P-option",
    "deribit-BTC-12JUL25-114000-C-option",
    "deribit-BTC-12JUL25-114000-P-option",
    "deribit-BTC-12JUL25-115000-C-option",
    "deribit-BTC-12JUL25-115000-P-option",
    "deribit-BTC-12JUL25-115500-C-option",
    "deribit-BTC-12JUL25-115500-P-option",
    "deribit-BTC-12JUL25-116000-C-option",
    "deribit-BTC-12JUL25-116000-P-option",
    "deribit-BTC-12JUL25-116500-C-option",
    "deribit-BTC-12JUL25-116500-P-option",
    "deribit-BTC-12JUL25-117000-C-option",
    "deribit-BTC-12JUL25-117000-P-option",
    "deribit-BTC-12JUL25-117500-C-option",
    "deribit-BTC-12JUL25-117500-P-option",
    "deribit-BTC-12JUL25-118000-C-option",
    "deribit-BTC-12JUL25-118000-P-option",
    "deribit-BTC-12JUL25-118500-C-option",
    "deribit-BTC-12JUL25-118500-P-option",
    "deribit-BTC-12JUL25-119000-C-option",
    "deribit-BTC-12JUL25-119000-P-option",
    "deribit-BTC-12JUL25-119500-C-option",
    "deribit-BTC-12JUL25-119500-P-option",
    "deribit-BTC-12JUL25-120000-C-option",
    "deribit-BTC-12JUL25-120000-P-option",
    "deribit-BTC-12JUL25-120500-C-option",
    "deribit-BTC-12JUL25-120500-P-option",
    "deribit-BTC-12JUL25-121000-C-option",
    "deribit-BTC-12JUL25-121000-P-option",
    "deribit-BTC-12JUL25-122000-C-option",
    "deribit-BTC-12JUL25-122000-P-option",
    "deribit-BTC-12JUL25-123000-C-option",
    "deribit-BTC-12JUL25-123000-P-option",
    "deribit-BTC-12JUL25-124000-C-option",
    "deribit-BTC-12JUL25-124000-P-option",
    "deribit-BTC-12JUL25-125000-C-option",
    "deribit-BTC-12JUL25-125000-P-option",
    "deribit-BTC-12JUL25-126000-C-option",
    "deribit-BTC-12JUL25-126000-P-option",
    "deribit-BTC-12JUL25-128000-C-option",
    "deribit-BTC-12JUL25-128000-P-option",
    "deribit-BTC-12JUL25-130000-C-option",
    "deribit-BTC-12JUL25-130000-P-option",
    "deribit-BTC-12JUL25-96000-C-option",
    "deribit-BTC-12JUL25-96000-P-option",
    "deribit-BTC-12JUL25-98000-C-option",
    "deribit-BTC-12JUL25-98000-P-option",
    "deribit-BTC-13JUL25-100000-C-option",
    "deribit-BTC-13JUL25-100000-P-option",
    "deribit-BTC-13JUL25-102000-C-option",
    "deribit-BTC-13JUL25-102000-P-option",
    "deribit-BTC-13JUL25-104000-C-option",
    "deribit-BTC-13JUL25-104000-P-option",
    "deribit-BTC-13JUL25-106000-C-option",
    "deribit-BTC-13JUL25-106000-P-option",
    "deribit-BTC-13JUL25-107000-C-option",
    "deribit-BTC-13JUL25-107000-P-option",
    "deribit-BTC-13JUL25-108000-C-option",
    "deribit-BTC-13JUL25-108000-P-option",
    "deribit-BTC-13JUL25-109000-C-option",
    "deribit-BTC-13JUL25-109000-P-option",
    "deribit-BTC-13JUL25-110000-C-option",
    "deribit-BTC-13JUL25-110000-P-option",
    "deribit-BTC-13JUL25-111000-C-option",
    "deribit-BTC-13JUL25-111000-P-option",
    "deribit-BTC-13JUL25-112000-C-option",
    "deribit-BTC-13JUL25-112000-P-option",
    "deribit-BTC-13JUL25-113000-C-option",
    "deribit-BTC-13JUL25-113000-P-option",
    "deribit-BTC-13JUL25-114000-C-option",
    "deribit-BTC-13JUL25-114000-P-option",
    "deribit-BTC-13JUL25-115000-C-option",
    "deribit-BTC-13JUL25-115000-P-option",
    "deribit-BTC-13JUL25-115500-C-option",
    "deribit-BTC-13JUL25-115500-P-option",
    "deribit-BTC-13JUL25-116000-C-option",
    "deribit-BTC-13JUL25-116000-P-option"
]

def make_request(market):
    """Make HTTP request for a specific market and return response details"""
    # Create parameters copy and add the market
    request_params = params.copy()
    request_params['markets'] = market
    
    # Record start time
    start_time = time.time()
    
    try:
        # Make the request
        response = requests.get(base_url, params=request_params)
        
        # Calculate duration
        duration = time.time() - start_time
        
        # Get response text
        response_text = response.text
        
        return {
            'market': market,
            'duration': duration,
            'status_code': response.status_code,
            'response_preview': response_text[:50],
            'success': response.status_code == 200
        }
        
    except Exception as e:
        duration = time.time() - start_time
        return {
            'market': market,
            'duration': duration,
            'status_code': None,
            'response_preview': f"Error: {str(e)[:50]}",
            'success': False
        }

def main():
    """Main function to iterate through markets and make requests"""
    print(f"Starting requests for {len(markets)} markets...")
    print("-" * 80)
    
    total_start_time = time.time()
    successful_requests = 0
    failed_requests = 0
    
    for i, market in enumerate(markets, 1):
        print(f"[{i}/{len(markets)}] Processing: {market}")
        
        result = make_request(market)
        
        if result['success']:
            successful_requests += 1
            status = "✓"
        else:
            failed_requests += 1
            status = "✗"
        
        print(f"  {status} Duration: {result['duration']:.3f}s | Status: {result['status_code']}")
        print(f"  Response preview: {result['response_preview']}")
        print()
        
        # Optional: Add a small delay between requests to be respectful to the API
        time.sleep(0.1)
    
    total_duration = time.time() - total_start_time
    
    print("-" * 80)
    print(f"Summary:")
    print(f"  Total markets processed: {len(markets)}")
    print(f"  Successful requests: {successful_requests}")
    print(f"  Failed requests: {failed_requests}")
    print(f"  Total execution time: {total_duration:.3f}s")
    print(f"  Average time per request: {total_duration/len(markets):.3f}s")

if __name__ == "__main__":
    main()