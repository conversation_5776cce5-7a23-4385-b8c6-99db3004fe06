import random

def generate_row_sequence(num_rows=102, min_val=0.01, max_val=99.99):
    """
    Generate a sequence of ROW strings with random double values.
    
    Args:
        num_rows (int): Number of rows to generate (default: 102)
        min_val (float): Minimum value for random numbers (default: 0.01)
        max_val (float): Maximum value for random numbers (default: 99.99)
    
    Returns:
        str: Comma-separated ROW strings
    """
    rows = []
    
    for i in range(num_rows):
        # Generate two random values with 2 decimal precision
        val1 = round(random.uniform(min_val, max_val), 2)
        val2 = round(random.uniform(min_val, max_val), 2)
        
        # Create ROW string
        row = f"ROW({val1},{val2},NULL)"
        rows.append(row)
    
    return ",".join(rows)

# Generate the sequence
if __name__ == "__main__":
    # Set seed for reproducible results (optional - remove for truly random)
    # random.seed(42)
    
    result = generate_row_sequence()
    print(result)
    
    # Optional: Save to file
    # with open("row_sequence.txt", "w") as f:
    #     f.write(result)