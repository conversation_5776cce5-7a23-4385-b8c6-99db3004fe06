from coinmetrics.api_client import CoinMetricsClient
from dateutil.relativedelta import relativedelta

relevant_markets = [
    'coinbase-usdt-usd-spot',
    'kraken-usdc-usdt-spot',
    'kraken-usdt-usd-spot',
    'bitstamp-usdt-usd-spot',
    'bitstamp-usdt-eur-spot',
    'crypto.com-usdt-usd-spot',
    'lmax-usdc-usd-spot', # no BITAVO BITSO
    'okex-usdt-eur-spot',
    'binance-btc-usdt-spot',
    'binance-eth-usdt-spot',
    'binance-usdc-usdt-spot',
    'bybit-btc-usdt-spot',
    'bybit-eth-usdt-spot',
    'bybit-usdc-usdt-spot',
]
api_key = "jAwn15KyijB8h6Hqa3vM"
client = CoinMetricsClient(api_key)
q = client.get_market_quotes(
    markets=relevant_markets,
    granularity='1d',
    start_time='2024-01-01'

).export_to_json()
