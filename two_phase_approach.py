import datetime
import requests
import json
import time

def fetch_market_data(market, api_key, start_time='2024-01-01', page_size=10000):
    """
    Fetch market quotes data from CoinMetrics API using requests library.

    Args:
        market (str): Market identifier
        api_key (str): CoinMetrics API key
        start_time (str): Start time in YYYY-MM-DD format
        page_size (int): Number of records per page

    Returns:
        dict: JSON response from the API
    """
    base_url = "https://api.coinmetrics.io/v4/timeseries/market-quotes"

    params = {
        'markets': market,
        'start_time': start_time,
        'page_size': page_size,
        'paging_from': 'start',
        'api_key': api_key
    }

    try:
        print(f"{datetime.datetime.now()}: Making API request for market {market}")
        response = requests.get(base_url, params=params)
        response.raise_for_status()  # Raises an HTTPError for bad responses

        data = response.json()
        print(f"{datetime.datetime.now()}: Successfully fetched {len(data.get('data', []))} records for market {market}")

        return data

    except requests.exceptions.RequestException as e:
        print(f"{datetime.datetime.now()}: Error fetching data for market {market}: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"{datetime.datetime.now()}: Error parsing JSON response for market {market}: {e}")
        return None

def save_to_json(data, filename):
    """
    Save data to JSON file.

    Args:
        data (dict): Data to save
        filename (str): Output filename
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"{datetime.datetime.now()}: Saved data to {filename}")
    except Exception as e:
        print(f"{datetime.datetime.now()}: Error saving to {filename}: {e}")

# Configuration
relevant_markets = [
    'coinbase-usdt-usd-spot',
    'kraken-usdc-usdt-spot',
    'kraken-usdt-usd-spot',
    'bitstamp-usdt-usd-spot',
    'bitstamp-usdt-eur-spot',
    'crypto.com-usdt-usd-spot',
    'lmax-usdc-usd-spot',  # no BITAVO BITSO
    'okex-usdt-eur-spot',
    'binance-btc-usdt-spot',
    'binance-eth-usdt-spot',
    'binance-usdc-usdt-spot',
    'bybit-btc-usdt-spot',
    'bybit-eth-usdt-spot',
    'bybit-usdc-usdt-spot',
]

api_key = "4ze0LiIhSN10fy8iBnUH"

# Main execution
print(f"{datetime.datetime.now()}: Starting data collection for {len(relevant_markets)} markets")

for market in relevant_markets:
    print(f"\n{datetime.datetime.now()}: Processing market {market}")

    # Fetch data from API
    data = fetch_market_data(market, api_key)

    if data:
        # Save to JSON file
        filename = f"{market}.json"
        save_to_json(data, filename)

        # Add a small delay to be respectful to the API
        time.sleep(0.5)
    else:
        print(f"{datetime.datetime.now()}: Skipping {market} due to fetch error")

print(f"\n{datetime.datetime.now()}: Data collection completed")
