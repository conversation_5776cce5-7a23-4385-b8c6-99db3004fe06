from datetime import datetime, timezone


def microseconds_to_datetime(microseconds, use_utc=True):
    """
    Convert microseconds since Unix epoch to datetime object.

    Args:
        microseconds (int): Microseconds since Unix epoch (January 1, 1970)
        use_utc (bool): If True, return UTC datetime; if False, return local datetime

    Returns:
        datetime: Datetime object
    """
    # Convert microseconds to seconds
    seconds = microseconds / 1_000_000

    if use_utc:
        return datetime.fromtimestamp(seconds, tz=timezone.utc)
    else:
        return datetime.fromtimestamp(seconds)


def datetime_to_microseconds(dt):
    """
    Convert datetime object to microseconds since Unix epoch.

    Args:
        dt (datetime): Datetime object

    Returns:
        int: Microseconds since Unix epoch
    """
    return int(dt.timestamp() * 1_000_000)


# Example usage
if __name__ == "__main__":
    # Example microsecond timestamps
    test_microseconds = [
        1703131502123456789,  # January 20, 2022 00:00:00 UTC
        1703142602123456789,  # November 14, 2023 22:13:20 UTC
        1721088000000000,  # July 16, 2024 00:00:00 UTC (today's date)
    ]

    print("Converting microseconds to datetime:")
    print("-" * 50)

    for us in test_microseconds:
        # UTC datetime
        dt_utc = microseconds_to_datetime(us, use_utc=True)
        # Local datetime
        dt_local = microseconds_to_datetime(us, use_utc=False)

        print(f"Microseconds: {us}")
        print(f"UTC:          {dt_utc}")
        print(f"Local:        {dt_local}")
        print()

    # Convert current time to microseconds and back
    print("Current time conversion:")
    print("-" * 30)
    now = datetime.now(timezone.utc)
    now_microseconds = datetime_to_microseconds(now)
    now_converted = microseconds_to_datetime(now_microseconds)

    print(f"Original:     {now}")
    print(f"Microseconds: {now_microseconds}")
    print(f"Converted:    {now_converted}")
